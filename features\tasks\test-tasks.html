<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tasks Module Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #fe2c55;
            --secondary-color: #25f4ee;
            --background-color: #121212;
            --text-color: #ffffff;
            --card-bg: #1e1e1e;
            --hover-bg: #2d2d2d;
            --border-color: #333;
            --primary-color-transparent: rgba(254, 44, 85, 0.2);
            --secondary-color-transparent: rgba(37, 244, 238, 0.2);
        }
        
        body {
            background-color: var(--background-color);
            color: var(--text-color);
            padding: 20px;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--card-bg);
            padding: 30px;
            border-radius: 12px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
        }
        
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .test-pass {
            background-color: rgba(40, 167, 69, 0.2);
            border: 1px solid #28a745;
            color: #28a745;
        }
        
        .test-fail {
            background-color: rgba(220, 53, 69, 0.2);
            border: 1px solid #dc3545;
            color: #dc3545;
        }
        
        .test-warning {
            background-color: rgba(255, 193, 7, 0.2);
            border: 1px solid #ffc107;
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="bi bi-check-circle"></i> Tasks Module Test</h1>
        <p>Testing the modular tasks management system...</p>
        
        <div class="test-section">
            <h3>Core Module Tests</h3>
            <div id="core-tests"></div>
        </div>
        
        <div class="test-section">
            <h3>Filter Module Tests</h3>
            <div id="filter-tests"></div>
        </div>
        
        <div class="test-section">
            <h3>Notes Module Tests</h3>
            <div id="notes-tests"></div>
        </div>
        
        <div class="test-section">
            <h3>Links Module Tests</h3>
            <div id="links-tests"></div>
        </div>
        
        <div class="test-section">
            <h3>Integration Tests</h3>
            <div id="integration-tests"></div>
        </div>
    </div>

    <!-- Load the modules -->
    <script src="../core/tasksManager.js"></script>
    <script src="../core/currentTaskManager.js"></script>
    <script src="../filters/taskFilters.js"></script>
    <script src="../notes/task-notes.js"></script>
    <script src="../notes/task-notes-injector.js"></script>
    <script src="../links/taskLinks.js"></script>
    
    <script>
        // Test runner
        function runTests() {
            console.log('Starting Tasks Module Tests...');
            
            // Test Core Modules
            testCoreModules();
            testFilterModules();
            testNotesModules();
            testLinksModules();
            testIntegration();
        }
        
        function addTestResult(containerId, testName, passed, message = '') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${passed ? 'test-pass' : 'test-fail'}`;
            resultDiv.innerHTML = `
                <i class="bi ${passed ? 'bi-check-circle' : 'bi-x-circle'}"></i>
                <strong>${testName}:</strong> ${passed ? 'PASS' : 'FAIL'}
                ${message ? `<br><small>${message}</small>` : ''}
            `;
            container.appendChild(resultDiv);
        }
        
        function addWarning(containerId, testName, message) {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = 'test-result test-warning';
            resultDiv.innerHTML = `
                <i class="bi bi-exclamation-triangle"></i>
                <strong>${testName}:</strong> WARNING
                <br><small>${message}</small>
            `;
            container.appendChild(resultDiv);
        }
        
        function testCoreModules() {
            // Test TasksManager class
            try {
                const tasksManager = new TasksManager();
                addTestResult('core-tests', 'TasksManager Class', true, 'Class instantiated successfully');
                
                // Test methods exist
                const hasLoadTasks = typeof tasksManager.loadTasks === 'function';
                addTestResult('core-tests', 'TasksManager.loadTasks()', hasLoadTasks);
                
                const hasDisplayTasks = typeof tasksManager.displayTasks === 'function';
                addTestResult('core-tests', 'TasksManager.displayTasks()', hasDisplayTasks);
                
                const hasFilterTasks = typeof tasksManager.filterTasks === 'function';
                addTestResult('core-tests', 'TasksManager.filterTasks()', hasFilterTasks);
                
            } catch (error) {
                addTestResult('core-tests', 'TasksManager Class', false, error.message);
            }
            
            // Test CurrentTaskManager class
            try {
                const currentTaskManager = new CurrentTaskManager();
                addTestResult('core-tests', 'CurrentTaskManager Class', true, 'Class instantiated successfully');
                
                const hasCheckCurrentTask = typeof currentTaskManager.checkCurrentTask === 'function';
                addTestResult('core-tests', 'CurrentTaskManager.checkCurrentTask()', hasCheckCurrentTask);
                
            } catch (error) {
                addTestResult('core-tests', 'CurrentTaskManager Class', false, error.message);
            }
        }
        
        function testFilterModules() {
            // Test TaskFilters class
            try {
                // Create mock todoist integration
                const mockTodoist = {
                    getTasks: () => Promise.resolve([]),
                    getProjects: () => Promise.resolve([]),
                    getSections: () => Promise.resolve([])
                };
                
                const taskFilters = new TaskFilters(mockTodoist);
                addTestResult('filter-tests', 'TaskFilters Class', true, 'Class instantiated successfully');
                
                const hasApplyFilters = typeof taskFilters.applyFilters === 'function';
                addTestResult('filter-tests', 'TaskFilters.applyFilters()', hasApplyFilters);
                
                const hasClearFilters = typeof taskFilters.clearFilters === 'function';
                addTestResult('filter-tests', 'TaskFilters.clearFilters()', hasClearFilters);
                
            } catch (error) {
                addTestResult('filter-tests', 'TaskFilters Class', false, error.message);
            }
        }
        
        function testNotesModules() {
            // Test TaskNotesManager class
            try {
                const taskNotesManager = new TaskNotesManager();
                addTestResult('notes-tests', 'TaskNotesManager Class', true, 'Class instantiated successfully');
                
                const hasOpenNotesModal = typeof taskNotesManager.openNotesModal === 'function';
                addTestResult('notes-tests', 'TaskNotesManager.openNotesModal()', hasOpenNotesModal);
                
                const hasSaveNote = typeof taskNotesManager.saveNote === 'function';
                addTestResult('notes-tests', 'TaskNotesManager.saveNote()', hasSaveNote);
                
            } catch (error) {
                addTestResult('notes-tests', 'TaskNotesManager Class', false, error.message);
            }
        }
        
        function testLinksModules() {
            // Test TaskLinksManager class
            try {
                const taskLinksManager = new TaskLinksManager();
                addTestResult('links-tests', 'TaskLinksManager Class', true, 'Class instantiated successfully');
                
                const hasAddLink = typeof taskLinksManager.addLink === 'function';
                addTestResult('links-tests', 'TaskLinksManager.addLink()', hasAddLink);
                
                const hasRemoveLink = typeof taskLinksManager.removeLink === 'function';
                addTestResult('links-tests', 'TaskLinksManager.removeLink()', hasRemoveLink);
                
            } catch (error) {
                addTestResult('links-tests', 'TaskLinksManager Class', false, error.message);
            }
        }
        
        function testIntegration() {
            // Test global objects
            const hasTodoistIntegration = typeof window.todoistIntegration !== 'undefined';
            if (hasTodoistIntegration) {
                addTestResult('integration-tests', 'Todoist Integration', true, 'Global object available');
            } else {
                addWarning('integration-tests', 'Todoist Integration', 'Global object not available - may need to load todoistIntegration.js');
            }
            
            const hasFirebase = typeof window.firebase !== 'undefined';
            if (hasFirebase) {
                addTestResult('integration-tests', 'Firebase SDK', true, 'Global object available');
            } else {
                addWarning('integration-tests', 'Firebase SDK', 'Global object not available - Firebase features may not work');
            }
            
            const hasCrossTabSync = typeof window.crossTabSync !== 'undefined';
            if (hasCrossTabSync) {
                addTestResult('integration-tests', 'Cross-tab Sync', true, 'Global object available');
            } else {
                addWarning('integration-tests', 'Cross-tab Sync', 'Global object not available - cross-tab features may not work');
            }
            
            // Test localStorage access
            try {
                localStorage.setItem('test', 'value');
                localStorage.removeItem('test');
                addTestResult('integration-tests', 'LocalStorage Access', true, 'Read/write access confirmed');
            } catch (error) {
                addTestResult('integration-tests', 'LocalStorage Access', false, error.message);
            }
        }
        
        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
