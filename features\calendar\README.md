# 📅 Calendar & Scheduling Module

## Overview

The Calendar & Scheduling module provides a comprehensive calendar system with daily views, timetable management, and intelligent scheduling features. It integrates with task management, sleep schedules, and academic timetables to provide a unified time management experience.

## 🏗️ Module Structure

```
features/calendar/
├── daily/                          # Daily calendar view
│   ├── calendarManager.js         # Main calendar management logic
│   ├── calendar-views.js          # Calendar view rendering and navigation
│   ├── daily-calendar.html        # Daily calendar interface
│   └── daily-calendar.css         # Daily calendar styling
├── timetable/                      # Timetable management
│   ├── timetableAnalyzer.js       # Timetable analysis and optimization
│   └── timetableIntegration.js    # Integration with academic schedules
├── scheduling/                     # Schedule optimization
│   ├── scheduleManager.js         # Daily schedule management
│   ├── sleepScheduleManager.js    # Sleep schedule integration
│   └── sleepTimeCalculator.js     # Sleep time calculations
├── ui/                            # Shared UI components
│   └── calendar.css               # Common calendar styling
├── test-calendar.html             # Module testing interface
└── README.md                      # This documentation
```

## ✨ Features

### Daily Calendar View
- **Interactive Timeline**: 24-hour timeline with minute-level precision
- **Event Management**: Create, edit, delete, and move events
- **Drag & Drop**: Intuitive event manipulation with mouse interactions
- **Time Block Selection**: Click and drag to create new events
- **Event Overlapping**: Smart handling of overlapping events with visual offsets

### Event Types
- **Academic Events**: Classes, lectures, study sessions
- **Personal Events**: Meals, breaks, personal time
- **Sleep Events**: Sleep and wake times with buffer periods
- **Free Time**: Available time slots for scheduling

### Timetable Integration
- **Academic Schedule**: Integration with class timetables
- **Conflict Detection**: Automatic detection of scheduling conflicts
- **Optimization**: Intelligent scheduling suggestions
- **Analysis**: Time usage analysis and reporting

### Sleep Schedule Management
- **Sleep Tracking**: Track sleep and wake times
- **Buffer Periods**: Configurable buffer times for sleep preparation
- **Schedule Optimization**: Optimize daily schedule around sleep patterns
- **Cross-tab Sync**: Synchronize sleep schedules across browser tabs

### Task Integration
- **Task Visualization**: Display tasks on calendar timeline
- **Due Date Tracking**: Visual indicators for task deadlines
- **Completion Status**: Track task completion within calendar view
- **Priority Indicators**: Color-coded priority levels

## 🚀 Usage

### Basic Setup

1. **Include the module in your HTML:**
```html
<link href="features/calendar/daily/daily-calendar.css" rel="stylesheet">
<link href="features/calendar/ui/calendar.css" rel="stylesheet">
<script src="features/calendar/daily/calendarManager.js"></script>
<script src="features/calendar/daily/calendar-views.js"></script>
<script src="features/calendar/scheduling/scheduleManager.js"></script>
```

2. **Initialize the calendar:**
```javascript
// Calendar initializes automatically when DOM is loaded
// Access the global calendar manager: window.calendarManager
```

### API Usage

#### Calendar Management
```javascript
// Create new calendar manager instance
const calendar = new CalendarManager();

// Navigate to different dates
calendar.navigateDate(1);  // Next day
calendar.navigateDate(-1); // Previous day

// Update time range
calendar.updateTimeRange();

// Render calendar
calendar.render();
```

#### Event Operations
```javascript
// Create new event
const event = {
    id: 'unique-id',
    title: 'Study Session',
    type: 'academic',
    startTime: '09:00',
    endTime: '10:30',
    date: '2024-12-19'
};

// Add event to calendar
calendar.events.push(event);
calendar.saveEvents();
calendar.render();

// Update event
calendar.updateEvent('event-id', {
    title: 'Updated Title',
    startTime: '09:30'
});

// Delete event
calendar.deleteEvent('event-id');
```

#### Schedule Management
```javascript
// Save daily schedule
saveDailySchedule();

// Load saved schedule
loadDailySchedule();

// Set wake and sleep times
localStorage.setItem('wakeTime', '07:00');
localStorage.setItem('sleepTime', '23:00');
```

#### Calendar Views
```javascript
// Initialize calendar view manager
const viewManager = new CalendarViewManager();
await viewManager.initialize();

// Switch between views
await viewManager.switchView('daily');
await viewManager.switchView('weekly');
await viewManager.switchView('monthly');
await viewManager.switchView('yearly');

// Refresh tasks
await viewManager.refreshTasks();
```

## 🔧 Configuration

### Time Settings
```javascript
// Configure time display format
const timeFormat = {
    hour12: false,  // 24-hour format
    precision: 'minute'  // Minute-level precision
};

// Set calendar time range
const timeRange = {
    start: '06:00',
    end: '24:00'
};
```

### Event Types Configuration
```javascript
const eventTypes = {
    academic: {
        color: '#4f46e5',
        label: 'Academic'
    },
    personal: {
        color: '#06b6d4',
        label: 'Personal'
    },
    sleep: {
        color: '#8b5cf6',
        label: 'Sleep'
    },
    free: {
        color: '#10b981',
        label: 'Free Time'
    }
};
```

### Visual Settings
```css
:root {
    --calendar-grid-height: 1440px; /* 24 hours * 60 minutes */
    --pixels-per-minute: 1;
    --hour-height: 60px;
    --event-min-height: 30px;
    --event-overlap-offset: 20px;
}
```

## 🔗 Dependencies

### External Libraries
- **Bootstrap 5.3.2**: UI framework and icons
- **Bootstrap Icons**: Icon library
- **Font Awesome**: Additional icons

### Internal Dependencies
- `js/sideDrawer.js`: Navigation drawer
- `js/firebaseAuth.js`: Authentication
- `js/currentTaskManager.js`: Task management integration
- `js/cross-tab-sync.js`: Cross-tab synchronization
- `js/inject-header.js`: Header injection

### Data Sources
- **Local Storage**: Event persistence and settings
- **Firestore**: Cloud data synchronization
- **Academic Subjects**: Integration with academic management
- **Task System**: Integration with task management

## 🧪 Testing

Run the test suite by opening `test-calendar.html` in your browser:

```bash
# Open in browser
file:///path/to/features/calendar/test-calendar.html
```

The test suite verifies:
- Module file existence
- HTML/CSS loading
- JavaScript module integrity
- Live calendar functionality

## 🐛 Known Issues

1. **Time Zone Handling**: Limited timezone support, uses local browser timezone
2. **Mobile Responsiveness**: Some drag-and-drop features may be limited on mobile
3. **Event Persistence**: Requires server endpoint for cloud synchronization
4. **Browser Compatibility**: Drag-and-drop requires modern browser support

## 🔄 Integration Points

### Task Management
- Displays tasks on calendar timeline
- Shows task due dates and priorities
- Integrates with task completion tracking

### Academic System
- Integrates with class timetables
- Shows academic schedule conflicts
- Supports semester-based scheduling

### Sleep Management
- Tracks sleep and wake times
- Optimizes schedule around sleep patterns
- Provides sleep quality insights

### Cross-tab Synchronization
- Syncs calendar events across browser tabs
- Real-time updates when data changes
- Consistent state management

## 📈 Performance

- **Event Rendering**: Optimized for up to 100 events per day
- **Time Precision**: Minute-level accuracy with pixel-perfect positioning
- **Memory Usage**: Efficient DOM manipulation with event delegation
- **Data Persistence**: Automatic saving with 30-second intervals

## 🤝 Contributing

When modifying this module:

1. Maintain the modular structure
2. Update this README for new features
3. Test with `test-calendar.html`
4. Ensure cross-browser compatibility
5. Follow the existing code style
6. Update integration points as needed

## 📄 License

This module is part of the GPAce application and follows the same licensing terms.
