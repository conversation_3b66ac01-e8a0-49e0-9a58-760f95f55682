# Tasks Management Module

## Overview
The Tasks Management module provides comprehensive task management functionality including task display, filtering, notes, links, and attachments. It integrates with Todoist for task synchronization and supports real-time collaboration features.

## Module Structure

```
features/tasks/
├── core/                    # Core task management logic
│   ├── tasksManager.js      # Main task management class
│   └── currentTaskManager.js # Current task tracking
├── filters/                 # Task filtering functionality
│   └── taskFilters.js       # Task filtering and search
├── notes/                   # Task notes functionality
│   ├── task-notes.js        # Task notes manager
│   └── task-notes-injector.js # Notes UI injection
├── links/                   # Task links and attachments
│   ├── taskLinks.js         # Task links management
│   └── taskAttachments.js   # File attachments handling
├── ui/                      # User interface components
│   ├── tasks.html           # Main tasks page
│   ├── tasks.css            # Tasks styling
│   ├── task-display.css     # Task display styling
│   ├── task-notes.css       # Notes styling
│   └── taskLinks.css        # Links styling
└── README.md               # This file

```

## Core Components

### TasksManager (`core/tasksManager.js`)
Main task management class that handles:
- Task loading from Todoist
- Task display and rendering
- Task filtering and search
- Task completion toggling
- Authentication management

**Key Methods:**
- `loadTasks()` - Loads tasks from Todoist API
- `displayTasks(tasks)` - Renders tasks in the UI
- `filterTasks(searchTerm)` - Filters tasks by search term
- `showAuthenticationPrompt()` - Shows login modal

**Dependencies:**
- `window.todoistIntegration` - Todoist API integration
- `window.soundManager` - Sound effects

### CurrentTaskManager (`core/currentTaskManager.js`)
Tracks and displays the current active task based on calendar events and priority tasks.

**Key Methods:**
- `checkCurrentTask()` - Determines current task
- `findCurrentTask(tasks, currentTime)` - Finds task for current time
- `updateTaskDisplay(task)` - Updates UI with current task
- `setCurrentTask(task)` - Sets and persists current task

**Data Sources:**
- Calendar events from localStorage
- Priority tasks from localStorage

### TaskFilters (`filters/taskFilters.js`)
Provides advanced filtering capabilities for tasks.

**Key Methods:**
- `populateProjects()` - Loads project filter options
- `populateSections(projectId)` - Loads section filter options
- `applyFilters()` - Applies all active filters
- `clearFilters()` - Resets all filters

**Filter Types:**
- Priority level
- Project
- Section
- Date range

## Notes System

### TaskNotesManager (`notes/task-notes.js`)
Comprehensive notes management with cross-device synchronization.

**Features:**
- Add, edit, delete notes
- Cross-tab synchronization
- Firebase/Firestore integration
- Device identification
- Real-time updates

**Key Methods:**
- `openNotesModal(taskId, projectId)` - Opens notes modal
- `saveNote()` - Saves new note
- `editNote(noteId)` - Edits existing note
- `deleteNote(noteId)` - Deletes note

### Task Notes Injector (`notes/task-notes-injector.js`)
Automatically injects notes buttons into task displays.

**Functionality:**
- Modifies task HTML templates
- Adds notes buttons to task actions
- Integrates with existing task display functions

## Links and Attachments

### TaskLinksManager (`links/taskLinks.js`)
Manages links associated with tasks.

**Features:**
- Add/remove links
- Link type detection (YouTube, GitHub, documents, etc.)
- URL validation and sanitization
- Firebase/Firestore synchronization
- Cross-tab updates

**Key Methods:**
- `addLink(taskId, linkData)` - Adds new link
- `removeLink(taskId, linkId)` - Removes link
- `renderLinks(taskId, container)` - Displays links

### TaskAttachments (`links/taskAttachments.js`)
Handles file attachments with Google Drive integration.

**Features:**
- File upload and storage
- File preview (images, PDFs, documents)
- Google Drive integration
- Drag and drop support
- File type detection

**Dependencies:**
- Google Drive API
- File viewer utilities

## API Integration

### Todoist Integration
- Authentication management
- Task CRUD operations
- Project and section management
- Real-time synchronization

### Firebase/Firestore
- Notes storage and synchronization
- Links storage
- Cross-device data sync
- Real-time updates

### Google Drive
- File storage
- File sharing
- Authentication

## Usage

### Basic Task Management
```javascript
// Initialize task manager
const tasksManager = new TasksManager();

// Load and display tasks
await tasksManager.loadTasks();

// Filter tasks
tasksManager.filterTasks('search term');
```

### Notes Management
```javascript
// Open notes for a task
window.openTaskNotes(taskId, projectId);

// Notes are automatically saved and synchronized
```

### Links Management
```javascript
// Add a link to a task
await taskLinksManager.addLink(taskId, {
    url: 'https://example.com',
    title: 'Example Link',
    description: 'Optional description'
});
```

## Configuration

### Required Global Objects
- `window.todoistIntegration` - Todoist API integration
- `window.firebase` - Firebase SDK
- `window.auth` - Firebase authentication
- `window.db` - Firestore database
- `window.soundManager` - Sound effects (optional)
- `window.crossTabSync` - Cross-tab synchronization (optional)

### CSS Variables
The module uses CSS custom properties for theming:
- `--primary-color` - Primary brand color
- `--secondary-color` - Secondary brand color
- `--background-color` - Background color
- `--text-color` - Text color
- `--card-bg` - Card background
- `--border-color` - Border color

## Testing

To test the module functionality:

1. Open `features/tasks/ui/tasks.html` in a browser
2. Ensure Todoist integration is configured
3. Test task loading, filtering, and display
4. Test notes functionality
5. Test links and attachments

## Known Issues

- Requires active internet connection for Todoist sync
- Google Drive integration requires user authorization
- Some features may be limited without Firebase configuration

## Future Enhancements

- Offline task management
- Advanced task templates
- Task dependencies
- Bulk operations
- Enhanced file preview support
