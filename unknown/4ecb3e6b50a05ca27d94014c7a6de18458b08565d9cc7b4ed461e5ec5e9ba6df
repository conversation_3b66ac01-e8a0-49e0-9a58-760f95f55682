# Grind Mode Feature

## Overview

The Grind Mode is GPAce's flagship productivity feature that combines a Pomodoro timer with AI-powered research capabilities and interactive simulations. It provides a comprehensive study environment designed to maximize focus and learning efficiency through multi-sensory feedback and intelligent assistance.

## Features

- **Advanced Pomodoro Timer**: Customizable focus/break sessions with cross-tab synchronization
- **AI Research Assistant**: Powered by Google Gemini with support for text, image, and PDF analysis
- **Interactive Simulations**: AI-generated educational simulations with step-by-step navigation
- **Speech Synthesis**: Text-to-speech with word highlighting and voice customization
- **Energy Tracking**: Fatigue level monitoring with holographic visualizations
- **Cross-tab Sync**: Timer state persists across browser tabs and windows
- **Real-time Notifications**: Desktop notifications and audio alerts

## Directory Structure

```
features/grind-mode/
├── timer/                          # Pomodoro timer functionality
│   ├── pomodoroTimer.js            # Core timer logic and UI
│   └── pomodoroGlobal.js           # Global timer state management
├── ai-research/                    # AI research capabilities
│   └── ai-researcher.js            # AI-powered research with multi-modal support
├── simulation/                     # Interactive simulation system
│   └── simulation-enhancer.js     # Simulation enhancement with multi-sensory features
├── ui/                            # User interface files
│   ├── grind.html                 # Main grind mode page
│   ├── grind.css                  # Grind mode styles
│   ├── simulation-enhancer.css   # Simulation enhancement styles
│   └── grind-speech-synthesis.js # Speech synthesis functionality
└── README.md                     # This documentation
```

## Core Components

### 1. Pomodoro Timer (`timer/`)

**pomodoroTimer.js**
- Core timer functionality with customizable durations
- Visual progress indicators and animations
- Audio notifications and desktop alerts
- Integration with energy tracking system

**pomodoroGlobal.js**
- Cross-tab timer state synchronization
- Service worker integration for background notifications
- Persistent timer state in localStorage
- Automatic timer recovery after page refresh

### 2. AI Research Assistant (`ai-research/`)

**ai-researcher.js**
- Multi-modal AI research powered by Google Gemini
- Support for text queries, image analysis, and PDF processing
- Integration with Wolfram Alpha and Tavily APIs
- Real-time streaming responses with markdown rendering
- File upload with drag-and-drop support

### 3. Interactive Simulations (`simulation/`)

**simulation-enhancer.js**
- AI-generated educational simulations
- Step-by-step navigation with audio narration
- Haptic feedback for mobile devices
- Visual enhancements and progress tracking
- Pop-out simulation windows with resizing

### 4. User Interface (`ui/`)

**grind.html**
- Main grind mode interface
- Responsive design with modern UI components
- Integration of all grind mode features
- Accessibility features and keyboard shortcuts

**grind-speech-synthesis.js**
- Text-to-speech with word-by-word highlighting
- Voice selection and speed control
- Multiple highlight styles
- Pause/resume functionality

## API Reference

### Timer Functions

#### `startTimer()`
Starts the Pomodoro timer with the current settings.
- **Side Effects**: Updates UI, starts cross-tab sync, triggers notifications

#### `pauseTimer()`
Pauses the currently running timer.
- **Side Effects**: Stops timer updates, maintains state

#### `resetTimer()`
Resets the timer to default focus session.
- **Side Effects**: Clears timer state, updates UI

#### `startFocus()` / `startBreak()`
Switches between focus and break sessions.
- **Side Effects**: Updates timer duration and UI state

### AI Research Functions

#### `performAISearch(query, options)`
Performs AI-powered research on the given query.
- **Parameters**:
  - `query`: Search query string
  - `options`: Configuration object with API keys and model settings
- **Returns**: Promise resolving to research results

#### `handleFileUpload(file)`
Processes uploaded files (images/PDFs) for AI analysis.
- **Parameters**: `file` - File object to process
- **Returns**: Promise resolving to processed file data

### Speech Synthesis Functions

#### `speakSearchResults()`
Reads AI search results aloud with word highlighting.
- **Returns**: Boolean indicating success/failure

#### `pauseSpeaking()` / `stopSpeaking()`
Controls speech playback.
- **Side Effects**: Updates speech state and UI controls

### Simulation Functions

#### `generateSimulation(content)`
Creates an interactive simulation from research content.
- **Parameters**: `content` - Research content to simulate
- **Returns**: Promise resolving to simulation HTML

#### `enhanceSimulation(iframe)`
Adds multi-sensory enhancements to a simulation.
- **Parameters**: `iframe` - Simulation iframe element
- **Returns**: SimulationEnhancer instance

## Configuration

### API Keys
Configure API keys through the settings panel:
- **Google Gemini**: Required for AI research and simulation generation
- **Wolfram Alpha**: Optional for mathematical computations
- **Tavily**: Optional for web search capabilities

### Timer Settings
- **Focus Duration**: 1-60 minutes (default: 25)
- **Break Duration**: 1-30 minutes (default: 5)
- **Long Break**: After 4 pomodoros (default: 15 minutes)

### Speech Settings
- **Voice Selection**: Choose from available system voices
- **Speech Rate**: 0.5x to 3x speed
- **Highlight Styles**: Multiple visual highlighting options

## Dependencies

### External Dependencies
- Bootstrap 5.3.2 (UI framework)
- Font Awesome 6.5.1 (icons)
- PDF.js 3.11.174 (PDF processing)
- MathJax 3 (mathematical notation)
- Three.js 0.157.0 (3D visualizations)

### Internal Dependencies
- `js/common.js` - Common utility functions
- `js/cross-tab-sync.js` - Cross-tab synchronization
- `js/firestore.js` - Firebase integration
- `js/auth.js` - Authentication system
- `js/energyHologram.js` - Energy visualization

## Usage Examples

### Basic Timer Usage
```javascript
// Start a 25-minute focus session
startTimer();

// Switch to break mode
startBreak();

// Reset timer
resetTimer();
```

### AI Research
```javascript
// Perform text-based research
await performAISearch("Explain quantum computing");

// Upload and analyze an image
const file = document.getElementById('imageUpload').files[0];
await handleFileUpload(file);
```

### Speech Synthesis
```javascript
// Read search results aloud
speakSearchResults();

// Control playback
pauseSpeaking();
stopSpeaking();
```

## Known Issues

1. **Cross-browser Compatibility**: Speech synthesis voices vary between browsers
2. **Mobile Limitations**: Haptic feedback requires HTTPS on mobile devices
3. **API Rate Limits**: AI research may be throttled based on API usage
4. **Service Worker**: Background notifications require service worker registration

## Future Enhancements

- [ ] Offline mode with cached AI responses
- [ ] Advanced analytics and productivity insights
- [ ] Integration with calendar systems
- [ ] Collaborative study sessions
- [ ] Advanced simulation templates
- [ ] Voice commands for hands-free operation

## Contributing

When modifying the grind mode feature:

1. Test timer functionality across multiple browser tabs
2. Verify AI research works with different file types
3. Ensure speech synthesis works with various voices
4. Test simulation generation and enhancement
5. Update this README if adding new features
6. Maintain backward compatibility with existing data
