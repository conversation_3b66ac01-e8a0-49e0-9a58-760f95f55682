<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flashcards Module Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="./ui/flashcards.css" rel="stylesheet">
    <style>
        body {
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background-color: #2d2d2d;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .test-pass {
            background-color: #28a745;
            color: white;
        }
        .test-fail {
            background-color: #dc3545;
            color: white;
        }
        .test-pending {
            background-color: #ffc107;
            color: black;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">🧠 Flashcards Module Test</h1>
        
        <div class="test-section">
            <h3>Module Loading Tests</h3>
            <div id="moduleTests">
                <div class="test-result test-pending" id="sm2Test">
                    <i class="bi bi-clock"></i> Testing SM2 Algorithm Loading...
                </div>
                <div class="test-result test-pending" id="flashcardsTest">
                    <i class="bi bi-clock"></i> Testing Flashcards Core Loading...
                </div>
                <div class="test-result test-pending" id="integrationTest">
                    <i class="bi bi-clock"></i> Testing Integration Modules Loading...
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>SM2 Algorithm Test</h3>
            <div id="sm2AlgorithmTest">
                <div class="test-result test-pending" id="sm2ProcessTest">
                    <i class="bi bi-clock"></i> Testing SM2 Card Processing...
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>Flashcard UI Test</h3>
            <div class="flashcard-container" style="height: 200px;">
                <div class="flashcard" id="testFlashcard">
                    <div class="flashcard-inner">
                        <div class="flashcard-front">
                            <div class="flashcard-content">
                                <h4>Test Question</h4>
                                <p>What is the capital of France?</p>
                            </div>
                        </div>
                        <div class="flashcard-back">
                            <div class="flashcard-content">
                                <h4>Test Answer</h4>
                                <p>Paris</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="text-center mt-3">
                <button class="btn btn-primary" id="flipTestCard">
                    <i class="bi bi-arrow-repeat"></i> Flip Card
                </button>
            </div>
            <div id="uiTest">
                <div class="test-result test-pending" id="flipTest">
                    <i class="bi bi-clock"></i> Testing Card Flip Animation...
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>Test Results Summary</h3>
            <div id="testSummary">
                <div class="test-result test-pending">
                    <i class="bi bi-clock"></i> Running tests...
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Test Scripts -->
    <script>
        let testResults = {
            sm2: false,
            flashcards: false,
            integration: false,
            sm2Process: false,
            flip: false
        };

        function updateTestResult(testId, passed, message) {
            const element = document.getElementById(testId);
            if (passed) {
                element.className = 'test-result test-pass';
                element.innerHTML = `<i class="bi bi-check-circle"></i> ${message}`;
                testResults[testId.replace('Test', '')] = true;
            } else {
                element.className = 'test-result test-fail';
                element.innerHTML = `<i class="bi bi-x-circle"></i> ${message}`;
            }
            updateSummary();
        }

        function updateSummary() {
            const passed = Object.values(testResults).filter(r => r).length;
            const total = Object.keys(testResults).length;
            const summaryElement = document.getElementById('testSummary');
            
            if (passed === total) {
                summaryElement.innerHTML = `
                    <div class="test-result test-pass">
                        <i class="bi bi-check-circle"></i> All tests passed! (${passed}/${total})
                        <br><small>The flashcards module is working correctly and is ready for use.</small>
                    </div>
                `;
            } else {
                summaryElement.innerHTML = `
                    <div class="test-result test-${passed > 0 ? 'pending' : 'fail'}">
                        <i class="bi bi-${passed > 0 ? 'clock' : 'x-circle'}"></i> Tests: ${passed}/${total} passed
                        <br><small>Some tests are still running or have failed.</small>
                    </div>
                `;
            }
        }

        // Test SM2 Algorithm
        function testSM2() {
            try {
                // Try to load SM2 script
                const script = document.createElement('script');
                script.src = './core/sm2.js';
                script.onload = function() {
                    updateTestResult('sm2Test', true, 'SM2 Algorithm loaded successfully');
                    
                    // Test SM2 processing
                    setTimeout(() => {
                        try {
                            if (typeof window.SM2 !== 'undefined' && window.SM2.processCard) {
                                const testCard = {
                                    sm2: {
                                        repetitions: 0,
                                        easeFactor: 2.5,
                                        interval: 0,
                                        nextReview: new Date()
                                    }
                                };
                                const result = window.SM2.processCard(testCard, 4);
                                if (result && result.sm2) {
                                    updateTestResult('sm2ProcessTest', true, 'SM2 card processing works correctly');
                                } else {
                                    updateTestResult('sm2ProcessTest', false, 'SM2 card processing failed');
                                }
                            } else {
                                updateTestResult('sm2ProcessTest', false, 'SM2.processCard function not found');
                            }
                        } catch (error) {
                            updateTestResult('sm2ProcessTest', false, `SM2 processing error: ${error.message}`);
                        }
                    }, 500);
                };
                script.onerror = function() {
                    updateTestResult('sm2Test', false, 'Failed to load SM2 Algorithm');
                    updateTestResult('sm2ProcessTest', false, 'Cannot test SM2 processing - module failed to load');
                };
                document.head.appendChild(script);
            } catch (error) {
                updateTestResult('sm2Test', false, `SM2 loading error: ${error.message}`);
            }
        }

        // Test Flashcards Core
        function testFlashcards() {
            try {
                const script = document.createElement('script');
                script.src = './core/flashcards.js';
                script.onload = function() {
                    updateTestResult('flashcardsTest', true, 'Flashcards core loaded successfully');
                };
                script.onerror = function() {
                    updateTestResult('flashcardsTest', false, 'Failed to load Flashcards core');
                };
                document.head.appendChild(script);
            } catch (error) {
                updateTestResult('flashcardsTest', false, `Flashcards loading error: ${error.message}`);
            }
        }

        // Test Integration
        function testIntegration() {
            try {
                // Test if we can import the integration modules
                import('./integration/flashcardTaskIntegration.js')
                    .then(() => {
                        updateTestResult('integrationTest', true, 'Integration modules loaded successfully');
                    })
                    .catch((error) => {
                        updateTestResult('integrationTest', false, `Integration loading failed: ${error.message}`);
                    });
            } catch (error) {
                updateTestResult('integrationTest', false, `Integration error: ${error.message}`);
            }
        }

        // Test UI Flip
        function testFlip() {
            const flipBtn = document.getElementById('flipTestCard');
            const flashcard = document.getElementById('testFlashcard');
            
            flipBtn.addEventListener('click', function() {
                flashcard.classList.toggle('flipped');
                updateTestResult('flipTest', true, 'Card flip animation works correctly');
            });
            
            // Auto-test flip after 2 seconds
            setTimeout(() => {
                if (!testResults.flip) {
                    flipBtn.click();
                }
            }, 2000);
        }

        // Run all tests
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Starting flashcards module tests...');
            
            testSM2();
            testFlashcards();
            testIntegration();
            testFlip();
        });
    </script>
</body>
</html>
