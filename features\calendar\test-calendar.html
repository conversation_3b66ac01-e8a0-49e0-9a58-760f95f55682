<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calendar Module Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>📅 Calendar Module Test</h1>
        <p>Testing the modularized calendar & scheduling feature...</p>
        
        <div id="testResults">
            <div class="test-result info">
                <strong>Test Status:</strong> Running tests...
            </div>
        </div>

        <h2>📋 Module Structure Test</h2>
        <div id="moduleTests"></div>

        <h2>🖥️ Live Calendar Preview</h2>
        <iframe src="daily/daily-calendar.html" title="Calendar Module"></iframe>
    </div>

    <script>
        // Test the module structure
        function runModuleTests() {
            const results = document.getElementById('moduleTests');
            const testResults = document.getElementById('testResults');
            
            // Test 1: Check if calendar HTML loads
            fetch('daily/daily-calendar.html')
                .then(response => {
                    if (response.ok) {
                        addTestResult(results, 'success', '✅ Daily calendar HTML file loads successfully');
                    } else {
                        addTestResult(results, 'error', '❌ Daily calendar HTML file failed to load');
                    }
                })
                .catch(error => {
                    addTestResult(results, 'error', '❌ Error loading daily calendar HTML: ' + error.message);
                });

            // Test 2: Check if CSS files exist
            const cssFiles = [
                'daily/daily-calendar.css',
                'ui/calendar.css'
            ];

            cssFiles.forEach(cssFile => {
                fetch(cssFile)
                    .then(response => {
                        if (response.ok) {
                            addTestResult(results, 'success', `✅ ${cssFile} exists`);
                        } else {
                            addTestResult(results, 'error', `❌ ${cssFile} not found`);
                        }
                    })
                    .catch(error => {
                        addTestResult(results, 'error', `❌ Error loading ${cssFile}: ${error.message}`);
                    });
            });

            // Test 3: Check JavaScript modules
            const jsModules = [
                'daily/calendarManager.js',
                'daily/calendar-views.js',
                'timetable/timetableAnalyzer.js',
                'timetable/timetableIntegration.js',
                'scheduling/scheduleManager.js',
                'scheduling/sleepScheduleManager.js',
                'scheduling/sleepTimeCalculator.js'
            ];

            jsModules.forEach(module => {
                fetch(module)
                    .then(response => {
                        if (response.ok) {
                            addTestResult(results, 'success', `✅ ${module} exists`);
                        } else {
                            addTestResult(results, 'error', `❌ ${module} not found`);
                        }
                    })
                    .catch(error => {
                        addTestResult(results, 'error', `❌ Error loading ${module}: ${error.message}`);
                    });
            });

            // Update overall status
            setTimeout(() => {
                testResults.innerHTML = '<div class="test-result success"><strong>Test Status:</strong> Module structure tests completed!</div>';
            }, 2000);
        }

        function addTestResult(container, type, message) {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            container.appendChild(div);
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', runModuleTests);
    </script>
</body>
</html>
